"""
五子棋棋型识别和评估模块
实现精确的棋型识别和评估算法
"""

class PatternRecognizer:
    def __init__(self):
        # 棋型评分表 - 重新设计的更精确的评分系统
        self.pattern_scores = {
            # 必胜棋型
            'FIVE': 1000000,           # 五连
            'OPEN_FOUR': 100000,       # 活四 _XXXX_
            'FOUR': 10000,             # 冲四 XXXX_ 或 _XXXX
            
            # 强势棋型
            'OPEN_THREE': 1000,        # 活三 _XXX_
            'SLEEP_THREE': 100,        # 眠三 XXX_ 或 _XXX
            'OPEN_TWO': 100,           # 活二 _XX_
            'SLEEP_TWO': 10,           # 眠二 XX_ 或 _XX
            
            # 组合棋型加分
            'DOUBLE_THREE': 50000,     # 双活三
            'THREE_FOUR': 50000,       # 冲四活三
            'DOUBLE_FOUR': 200000,     # 双冲四
            
            # 基础分值
            'ONE': 1,                  # 单子
        }
        
        # 方向向量：水平、垂直、主对角线、副对角线
        self.directions = [(1, 0), (0, 1), (1, 1), (1, -1)]
    
    def analyze_position(self, board, x, y, player):
        """分析某个位置的所有棋型"""
        patterns = []
        
        for dx, dy in self.directions:
            pattern = self.analyze_line(board, x, y, dx, dy, player)
            if pattern != 'NONE':
                patterns.append(pattern)
        
        return patterns
    
    def analyze_line(self, board, x, y, dx, dy, player):
        """分析某个方向的棋型"""
        # 获取该方向的棋子序列
        line = self.get_line_sequence(board, x, y, dx, dy, 9)  # 获取9个位置的序列
        
        # 找到当前位置在序列中的索引
        center_idx = 4
        
        # 分析棋型
        return self.classify_pattern(line, center_idx, player)
    
    def get_line_sequence(self, board, x, y, dx, dy, length):
        """获取指定方向的棋子序列"""
        sequence = []
        start_offset = -(length // 2)
        
        for i in range(length):
            offset = start_offset + i
            nx = x + dx * offset
            ny = y + dy * offset
            
            if 0 <= nx < len(board[0]) and 0 <= ny < len(board):
                sequence.append(board[ny][nx])
            else:
                sequence.append(-1)  # 边界标记
        
        return sequence
    
    def classify_pattern(self, line, center_idx, player):
        """分类棋型"""
        if line[center_idx] != player:
            return 'NONE'
        
        # 向左右扩展，统计连续的己方棋子
        left_count = 0
        right_count = 0
        
        # 向左统计
        for i in range(center_idx - 1, -1, -1):
            if line[i] == player:
                left_count += 1
            else:
                break
        
        # 向右统计
        for i in range(center_idx + 1, len(line)):
            if line[i] == player:
                right_count += 1
            else:
                break
        
        total_count = left_count + right_count + 1
        
        # 检查两端的情况
        left_blocked = (center_idx - left_count - 1 < 0 or 
                       line[center_idx - left_count - 1] != 0)
        right_blocked = (center_idx + right_count + 1 >= len(line) or 
                        line[center_idx + right_count + 1] != 0)
        
        # 根据连子数和阻挡情况分类
        if total_count >= 5:
            return 'FIVE'
        elif total_count == 4:
            if not left_blocked and not right_blocked:
                return 'OPEN_FOUR'
            else:
                return 'FOUR'
        elif total_count == 3:
            if not left_blocked and not right_blocked:
                return 'OPEN_THREE'
            else:
                return 'SLEEP_THREE'
        elif total_count == 2:
            if not left_blocked and not right_blocked:
                return 'OPEN_TWO'
            else:
                return 'SLEEP_TWO'
        elif total_count == 1:
            return 'ONE'
        
        return 'NONE'
    
    def evaluate_position(self, board, x, y, player):
        """评估位置的总分值"""
        # 模拟落子
        original = board[y][x]
        board[y][x] = player
        
        patterns = self.analyze_position(board, x, y, player)
        score = 0
        
        # 基础棋型分值
        for pattern in patterns:
            score += self.pattern_scores.get(pattern, 0)
        
        # 检查组合棋型
        score += self.evaluate_combinations(patterns)
        
        # 位置价值加分
        score += self.evaluate_position_value(board, x, y)
        
        # 恢复原状
        board[y][x] = original
        
        return score
    
    def evaluate_combinations(self, patterns):
        """评估棋型组合的额外分值"""
        score = 0
        
        # 双活三
        if patterns.count('OPEN_THREE') >= 2:
            score += self.pattern_scores['DOUBLE_THREE']
        
        # 冲四活三
        if 'FOUR' in patterns and 'OPEN_THREE' in patterns:
            score += self.pattern_scores['THREE_FOUR']
        
        # 双冲四
        if patterns.count('FOUR') >= 2:
            score += self.pattern_scores['DOUBLE_FOUR']
        
        return score
    
    def evaluate_position_value(self, board, x, y):
        """评估位置本身的价值"""
        board_size = len(board)
        center = board_size // 2
        
        # 距离中心的价值
        distance_to_center = abs(x - center) + abs(y - center)
        center_value = max(0, 20 - distance_to_center * 2)
        
        # 周围棋子密度价值
        density_value = self.calculate_density_value(board, x, y)
        
        return center_value + density_value
    
    def calculate_density_value(self, board, x, y):
        """计算周围棋子密度价值"""
        density = 0
        board_size = len(board)
        
        for dy in range(-2, 3):
            for dx in range(-2, 3):
                if dx == 0 and dy == 0:
                    continue
                
                nx, ny = x + dx, y + dy
                if 0 <= nx < board_size and 0 <= ny < board_size:
                    if board[ny][nx] != 0:
                        distance = max(abs(dx), abs(dy))
                        density += 5 - distance
        
        return min(density, 50)  # 限制最大密度价值
    
    def find_threats(self, board, player):
        """找到所有威胁位置"""
        threats = []
        board_size = len(board)
        
        for y in range(board_size):
            for x in range(board_size):
                if board[y][x] == 0:
                    patterns = self.analyze_position(board, x, y, player)
                    
                    # 检查是否为威胁位置
                    if any(p in ['FIVE', 'OPEN_FOUR', 'FOUR'] for p in patterns):
                        threat_level = self.calculate_threat_level(patterns)
                        threats.append((x, y, threat_level))
        
        # 按威胁等级排序
        threats.sort(key=lambda t: t[2], reverse=True)
        return threats
    
    def calculate_threat_level(self, patterns):
        """计算威胁等级"""
        max_score = 0
        for pattern in patterns:
            score = self.pattern_scores.get(pattern, 0)
            max_score = max(max_score, score)
        return max_score
    
    def is_winning_move(self, board, x, y, player):
        """检查是否为必胜手"""
        board[y][x] = player
        patterns = self.analyze_position(board, x, y, player)
        board[y][x] = 0
        
        return 'FIVE' in patterns
    
    def has_double_threat(self, board, x, y, player):
        """检查是否形成双重威胁"""
        board[y][x] = player
        
        threat_count = 0
        for dy in range(len(board)):
            for dx in range(len(board[0])):
                if board[dy][dx] == 0:
                    patterns = self.analyze_position(board, dx, dy, player)
                    if any(p in ['FIVE', 'OPEN_FOUR'] for p in patterns):
                        threat_count += 1
                        if threat_count >= 2:
                            board[y][x] = 0
                            return True
        
        board[y][x] = 0
        return False
